package com.example.splitexpenses.util

import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.util.Date

class CsvUtilTest {

    private lateinit var testGroup: GroupData
    private lateinit var testExpense: Expense

    @Before
    fun setup() {
        // Create test data
        testExpense = Expense(
            id = "test-expense-id",
            amount = 50.0,
            description = "Test Expense",
            paidBy = "Alice",
            splitBetween = listOf("Alice", "Bob"),
            category = "Food",
            date = Date().time,
            timestamp = Date().time,
            isCategoryLocked = true
        )

        val testCategories = listOf(
            Category("Food", "🍔", listOf("food", "restaurant")),
            Category("Transport", "🚗", listOf("car", "bus"))
        )

        val testMemberAvatars = mapOf(
            "Alice" to "😊",
            "<PERSON>" to "😎"
        )

        testGroup = GroupData(
            id = "test-group-id",
            name = "Test Group",
            members = listOf("Alice", "Bob"),
            expenses = listOf(testExpense),
            memberUidMap = mapOf("Alice" to "device1"),
            allowedUsers = listOf("device1"),
            creatorUid = "device1",
            categories = testCategories,
            memberAvatars = testMemberAvatars
        )
    }

    @Test
    fun `test export and import round trip`() {
        // Export the group to CSV
        val outputStream = ByteArrayOutputStream()
        val exportResult = CsvUtil.exportGroupToCsv(testGroup, outputStream)
        assertTrue("Export should succeed", exportResult)

        // Get the CSV content
        val csvContent = outputStream.toString()
        println("Exported CSV content:\n$csvContent")

        // Import the CSV content back
        val inputStream = ByteArrayInputStream(csvContent.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Verify the import was successful
        assertTrue("Import should succeed", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        // Verify the imported group data
        val importedGroup = importResult.group!!
        assertEquals("Group name should match", testGroup.name, importedGroup.name)
        assertEquals("Members should match", testGroup.members, importedGroup.members)

        // Verify categories were imported correctly
        assertEquals("Number of categories should match", testGroup.categories.size, importedGroup.categories.size)
        assertEquals("Category names should match",
            testGroup.categories.map { it.name },
            importedGroup.categories.map { it.name }
        )
        assertEquals("Category emojis should match",
            testGroup.categories.map { it.emoji },
            importedGroup.categories.map { it.emoji }
        )

        // Verify member avatars were imported correctly
        assertEquals("Member avatars should match", testGroup.memberAvatars, importedGroup.memberAvatars)

        // Verify user management fields were imported correctly
        assertEquals("Member UID map should match", testGroup.memberUidMap, importedGroup.memberUidMap)
        assertEquals("Creator UID should match", testGroup.creatorUid, importedGroup.creatorUid)
        // Note: allowedUsers is managed by the repository, not the CSV import

        // Verify expenses were imported correctly
        assertEquals("Number of expenses should match", testGroup.expenses.size, importedGroup.expenses.size)
        val importedExpense = importedGroup.expenses.first()
        assertEquals("Expense amount should match", testExpense.amount, importedExpense.amount, 0.001)
        assertEquals("Expense description should match", testExpense.description, importedExpense.description)
        assertEquals("Expense paidBy should match", testExpense.paidBy, importedExpense.paidBy)
        assertEquals("Expense splitBetween should match", testExpense.splitBetween, importedExpense.splitBetween)
        assertEquals("Expense category should match", testExpense.category, importedExpense.category)
        assertEquals("Expense isCategoryLocked should match", testExpense.isCategoryLocked, importedExpense.isCategoryLocked)
    }

    @Test
    fun `test import with trailing empty fields`() {
        // Create a CSV with trailing semicolons (empty fields) that should be automatically trimmed
        val csvWithTrailingFields = """
            name;members;memberAvatars;categories;;;
            Test Group;Alice|Bob;Alice=😊;Bob=😎;Food~🍔~food^restaurant;Transport~🚗~car^bus;;
            amount;description;paidBy;splitBetween;category;date;isCategoryLocked;;
            50.00;Test Expense;Alice;Alice|Bob;Food;2024-01-01;true;;
        """.trimIndent()

        println("CSV with trailing fields:\n$csvWithTrailingFields")

        // Import the CSV content
        val inputStream = ByteArrayInputStream(csvWithTrailingFields.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Print any errors for debugging
        if (importResult.errors.isNotEmpty()) {
            println("Import errors:")
            importResult.errors.forEach { error ->
                println("  Line ${error.lineNumber}: ${error.message}")
            }
        }

        // Print any warnings for debugging
        if (importResult.warnings.isNotEmpty()) {
            println("Import warnings:")
            importResult.warnings.forEach { warning ->
                println("  Line ${warning.lineNumber}: ${warning.message}")
            }
        }

        // Verify the import was successful despite trailing empty fields
        assertTrue("Import should succeed even with trailing empty fields", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        // Verify the imported group data
        val importedGroup = importResult.group!!
        assertEquals("Group name should match", "Test Group", importedGroup.name)
        assertEquals("Members should match", listOf("Alice", "Bob"), importedGroup.members)

        // Verify expenses were imported correctly
        assertEquals("Number of expenses should match", 1, importedGroup.expenses.size)
        val importedExpense = importedGroup.expenses.first()
        assertEquals("Expense amount should match", 50.0, importedExpense.amount, 0.001)
        assertEquals("Expense description should match", "Test Expense", importedExpense.description)
        assertEquals("Expense paidBy should match", "Alice", importedExpense.paidBy)
        assertEquals("Expense splitBetween should match", listOf("Alice", "Bob"), importedExpense.splitBetween)
        assertEquals("Expense category should match", "Food", importedExpense.category)
        assertEquals("Expense isCategoryLocked should match", true, importedExpense.isCategoryLocked)
    }

    @Test
    fun `test import with various trailing delimiter patterns`() {
        // Test different patterns of trailing delimiters
        val testCases = listOf(
            "name;members;memberAvatars;categories;",      // Single trailing delimiter
            "name;members;memberAvatars;categories;;",     // Double trailing delimiter
            "name;members;memberAvatars;categories;;;",    // Triple trailing delimiter
            "name;members;memberAvatars;categories;;;;;",  // Many trailing delimiters
            "name;members;memberAvatars;categories;;;;;;;;;;;;;;;" // Extreme trailing delimiters
        )

        testCases.forEach { headerLine ->
            println("Testing header: '$headerLine'")

            val csvContent = """
                $headerLine
                Test Group;Alice|Bob;;
                amount;description;paidBy;splitBetween;category;date;isCategoryLocked
                50.00;Test Expense;Alice;Alice|Bob;Food;2024-01-01;true
            """.trimIndent()

            val inputStream = ByteArrayInputStream(csvContent.toByteArray())
            val importResult = CsvUtil.importGroupFromCsv(inputStream)

            // Print any errors for debugging
            if (importResult.errors.isNotEmpty()) {
                println("  Import errors for '$headerLine':")
                importResult.errors.forEach { error ->
                    println("    Line ${error.lineNumber}: ${error.message}")
                }
            }

            // Print any warnings for debugging
            if (importResult.warnings.isNotEmpty()) {
                println("  Import warnings for '$headerLine':")
                importResult.warnings.forEach { warning ->
                    println("    Line ${warning.lineNumber}: ${warning.message}")
                }
            }

            // Each test case should succeed
            assertTrue("Import should succeed for header: '$headerLine'", importResult.success)
            assertTrue("Import should have no errors for header: '$headerLine'", importResult.errors.isEmpty())

            val importedGroup = importResult.group!!
            assertEquals("Group name should match for header: '$headerLine'", "Test Group", importedGroup.name)
        }
    }

    @Test
    fun `test delimiter detection with semicolon CSV`() {
        // Test that semicolon delimiter is correctly detected
        val csvContent = """
            name;members;memberAvatars;categories
            Test Group;Alice|Bob;;
            amount;description;paidBy;splitBetween;category;date;isCategoryLocked
            50.00;Test Expense;Alice;Alice|Bob;Food;2024-01-01;true
        """.trimIndent()

        val inputStream = ByteArrayInputStream(csvContent.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Print any errors for debugging
        if (importResult.errors.isNotEmpty()) {
            println("Import errors:")
            importResult.errors.forEach { error ->
                println("  Line ${error.lineNumber}: ${error.message}")
            }
        }

        assertTrue("Import should succeed with semicolon delimiter", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        val importedGroup = importResult.group!!
        assertEquals("Group name should match", "Test Group", importedGroup.name)
        assertEquals("Members should match", listOf("Alice", "Bob"), importedGroup.members)
    }

    @Test
    fun `test enhanced CSV format with user management fields`() {
        // Create a CSV with the new enhanced format including user management fields
        val csvWithUserManagement = """
            name,members,memberAvatars,categories,memberUidMap,creatorUid
            Enhanced Group,Alice|Bob|Charlie,Alice=😊;Bob=😎,Food~🍔~food^restaurant;Transport~🚗~car^bus,Alice=device1;Bob=device2,device1
            amount,description,paidBy,splitBetween,category,date,isCategoryLocked
            50.00,Test Expense,Alice,Alice|Bob,Food,2024-01-01,true
            25.50,Transport Cost,Bob,Alice|Bob|Charlie,Transport,2024-01-02,false
        """.trimIndent()

        println("Enhanced CSV with user management:\n$csvWithUserManagement")

        // Import the CSV content
        val inputStream = ByteArrayInputStream(csvWithUserManagement.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Print any errors for debugging
        if (importResult.errors.isNotEmpty()) {
            println("Import errors:")
            importResult.errors.forEach { error ->
                println("  Line ${error.lineNumber}: ${error.message}")
            }
        }

        // Print any warnings for debugging
        if (importResult.warnings.isNotEmpty()) {
            println("Import warnings:")
            importResult.warnings.forEach { warning ->
                println("  Line ${warning.lineNumber}: ${warning.message}")
            }
        }

        // Verify the import was successful
        assertTrue("Import should succeed with enhanced format", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        // Verify the imported group data
        val importedGroup = importResult.group!!
        assertEquals("Group name should match", "Enhanced Group", importedGroup.name)
        assertEquals("Members should match", listOf("Alice", "Bob", "Charlie"), importedGroup.members)

        // Verify user management fields
        val expectedMemberUidMap = mapOf("Alice" to "device1", "Bob" to "device2")
        assertEquals("Member UID map should match", expectedMemberUidMap, importedGroup.memberUidMap)
        assertEquals("Creator UID should match", "device1", importedGroup.creatorUid)

        // Verify member avatars
        val expectedAvatars = mapOf("Alice" to "😊", "Bob" to "😎")
        assertEquals("Member avatars should match", expectedAvatars, importedGroup.memberAvatars)

        // Verify categories
        assertEquals("Should have 2 categories", 2, importedGroup.categories.size)
        val foodCategory = importedGroup.categories.find { it.name == "Food" }
        val transportCategory = importedGroup.categories.find { it.name == "Transport" }

        assertEquals("Food category emoji should match", "🍔", foodCategory?.emoji)
        assertEquals("Food category keywords should match", listOf("food", "restaurant"), foodCategory?.keywords)
        assertEquals("Transport category emoji should match", "🚗", transportCategory?.emoji)
        assertEquals("Transport category keywords should match", listOf("car", "bus"), transportCategory?.keywords)

        // Verify expenses
        assertEquals("Should have 2 expenses", 2, importedGroup.expenses.size)

        val firstExpense = importedGroup.expenses.find { it.description == "Test Expense" }!!
        assertEquals("First expense amount should match", 50.0, firstExpense.amount, 0.001)
        assertEquals("First expense paidBy should match", "Alice", firstExpense.paidBy)
        assertEquals("First expense splitBetween should match", listOf("Alice", "Bob"), firstExpense.splitBetween)
        assertEquals("First expense category should match", "Food", firstExpense.category)
        assertTrue("First expense should be category locked", firstExpense.isCategoryLocked)

        val secondExpense = importedGroup.expenses.find { it.description == "Transport Cost" }!!
        assertEquals("Second expense amount should match", 25.5, secondExpense.amount, 0.001)
        assertEquals("Second expense paidBy should match", "Bob", secondExpense.paidBy)
        assertEquals("Second expense splitBetween should match", listOf("Alice", "Bob", "Charlie"), secondExpense.splitBetween)
        assertEquals("Second expense category should match", "Transport", secondExpense.category)
        assertTrue("Second expense should not be category locked", !secondExpense.isCategoryLocked)
    }

    @Test
    fun `test backward compatibility with old CSV format`() {
        // Create a CSV with the old format (without user management fields)
        val oldFormatCsv = """
            name,members,memberAvatars,categories
            Old Format Group,Alice|Bob,Alice=😊;Bob=😎,Food~🍔~food^restaurant
            amount,description,paidBy,splitBetween,category,date,isCategoryLocked
            30.00,Old Format Expense,Alice,Alice|Bob,Food,2024-01-01,true
        """.trimIndent()

        println("Old format CSV:\n$oldFormatCsv")

        // Import the CSV content
        val inputStream = ByteArrayInputStream(oldFormatCsv.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Print any errors for debugging
        if (importResult.errors.isNotEmpty()) {
            println("Import errors:")
            importResult.errors.forEach { error ->
                println("  Line ${error.lineNumber}: ${error.message}")
            }
        }

        // Verify the import was successful
        assertTrue("Import should succeed with old format", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        // Verify the imported group data
        val importedGroup = importResult.group!!
        assertEquals("Group name should match", "Old Format Group", importedGroup.name)
        assertEquals("Members should match", listOf("Alice", "Bob"), importedGroup.members)

        // Verify user management fields are empty (as expected for old format)
        assertTrue("Member UID map should be empty for old format", importedGroup.memberUidMap.isEmpty())
        assertTrue("Creator UID should be empty for old format", importedGroup.creatorUid.isEmpty())

        // Verify member avatars still work
        val expectedAvatars = mapOf("Alice" to "😊", "Bob" to "😎")
        assertEquals("Member avatars should match", expectedAvatars, importedGroup.memberAvatars)

        // Verify expenses
        assertEquals("Should have 1 expense", 1, importedGroup.expenses.size)
        val expense = importedGroup.expenses.first()
        assertEquals("Expense amount should match", 30.0, expense.amount, 0.001)
        assertEquals("Expense description should match", "Old Format Expense", expense.description)
    }

    @Test
    fun `test extreme trailing fields case`() {
        // Test the exact case from the error message
        val csvContent = """
            name;members;memberAvatars;categories;;;;;;;;;;;;;;
            Test Group;Alice|Bob;;
            amount;description;paidBy;splitBetween;category;date;isCategoryLocked
            50.00;Test Expense;Alice;Alice|Bob;Food;2024-01-01;true
        """.trimIndent()

        println("Testing extreme trailing fields CSV:")
        println(csvContent)

        val inputStream = ByteArrayInputStream(csvContent.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Print any errors for debugging
        if (importResult.errors.isNotEmpty()) {
            println("Import errors:")
            importResult.errors.forEach { error ->
                println("  Line ${error.lineNumber}: ${error.message}")
            }
        }

        // Print any warnings for debugging
        if (importResult.warnings.isNotEmpty()) {
            println("Import warnings:")
            importResult.warnings.forEach { warning ->
                println("  Line ${warning.lineNumber}: ${warning.message}")
            }
        }

        assertTrue("Import should succeed with extreme trailing fields", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        val importedGroup = importResult.group!!
        assertEquals("Group name should match", "Test Group", importedGroup.name)
        assertEquals("Members should match", listOf("Alice", "Bob"), importedGroup.members)
    }
}
