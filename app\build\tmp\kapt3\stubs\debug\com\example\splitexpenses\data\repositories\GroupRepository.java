package com.example.splitexpenses.data.repositories;

/**
 * Repository for managing groups with offline capability
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0016\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J,\u0010\"\u001a\u00020\b2\u0006\u0010#\u001a\u00020\b2\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\b0\r2\u0006\u0010%\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010&J\u0016\u0010\'\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010)J\u000e\u0010*\u001a\u00020\u000b2\u0006\u0010+\u001a\u00020,J\u000e\u0010-\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010.J\u0006\u0010/\u001a\u000200J.\u00101\u001a\u001a\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\r\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\r022\u0006\u0010(\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010)J\u0018\u00103\u001a\u0004\u0018\u00010\b2\u0006\u0010(\u001a\u00020\b2\u0006\u00104\u001a\u00020\bJ\u0010\u00105\u001a\u0002062\u0006\u00107\u001a\u00020\u000eH\u0002J\f\u00108\u001a\b\u0012\u0004\u0012\u00020:09J\u0010\u0010;\u001a\u0004\u0018\u00010\b2\u0006\u0010(\u001a\u00020\bJ\u001c\u0010<\u001a\b\u0012\u0004\u0012\u00020\b0\r2\u0006\u0010(\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010)J\"\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u00020@2\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u0010AJ\u000e\u0010B\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\bJ\u001e\u0010C\u001a\u00020D2\u0006\u0010(\u001a\u00020\b2\u0006\u00104\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010EJ\u000e\u0010F\u001a\u00020DH\u0086@\u00a2\u0006\u0002\u0010.J\u001e\u0010G\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\b2\u0006\u00104\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010EJ\u0006\u0010H\u001a\u00020DJ\u0006\u0010I\u001a\u00020DJ\u000e\u0010J\u001a\u00020D2\u0006\u0010(\u001a\u00020\bJ\u0006\u0010K\u001a\u00020DJ$\u0010L\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\b2\f\u0010M\u001a\b\u0012\u0004\u0012\u00020N0\rH\u0086@\u00a2\u0006\u0002\u0010OJ$\u0010P\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\b2\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\b0\rH\u0086@\u00a2\u0006\u0002\u0010OJ\u001e\u0010Q\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\b2\u0006\u0010R\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010EJ(\u0010S\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\b2\u0006\u00104\u001a\u00020\b2\b\u0010T\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u0010UJ&\u0010V\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\b2\u0006\u0010W\u001a\u00020\b2\u0006\u0010X\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010UR\u000e\u0010\u0007\u001a\u00020\bX\u0082D\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\r0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u001d\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\r0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0017R\u0019\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0017R\u0019\u0010\u001c\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0017R\u0019\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0017R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000b0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0017R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0017R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006Y"}, d2 = {"Lcom/example/splitexpenses/data/repositories/GroupRepository;", "", "offlineCapableRepository", "Lcom/example/splitexpenses/data/repositories/OfflineCapableRepository;", "localDataSource", "Lcom/example/splitexpenses/data/source/LocalDataSource;", "(Lcom/example/splitexpenses/data/repositories/OfflineCapableRepository;Lcom/example/splitexpenses/data/source/LocalDataSource;)V", "TAG", "", "_accessLost", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_availableGroups", "", "Lcom/example/splitexpenses/data/GroupData;", "_currentGroup", "_currentGroupError", "_groupsError", "_isLoadingCurrentGroup", "_isLoadingGroups", "accessLost", "Lkotlinx/coroutines/flow/StateFlow;", "getAccessLost", "()Lkotlinx/coroutines/flow/StateFlow;", "availableGroups", "getAvailableGroups", "currentGroup", "getCurrentGroup", "currentGroupError", "getCurrentGroupError", "groupsError", "getGroupsError", "isLoadingCurrentGroup", "isLoadingGroups", "createGroup", "name", "members", "currentUser", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteGroup", "groupId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "exportGroupToCsv", "outputStream", "Ljava/io/OutputStream;", "forceSyncOfflineChanges", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getContext", "Landroid/content/Context;", "getGroupMembersWithStatus", "Lkotlin/Pair;", "getMemberAvatar", "memberName", "getMostRecentExpenseDate", "", "group", "getPendingSyncCountFlow", "Lkotlinx/coroutines/flow/Flow;", "", "getSavedUserForGroup", "getUnassignedMembers", "importGroupFromCsv", "Lcom/example/splitexpenses/util/CsvImportResult;", "inputStream", "Ljava/io/InputStream;", "(Ljava/io/InputStream;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isCurrentUserGroupCreator", "joinGroup", "", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "loadAvailableGroups", "removeMemberAndKick", "resetAccessLost", "startListeningForAvailableGroups", "startListeningForCurrentGroup", "stopListeningForCurrentGroup", "updateGroupCategories", "categories", "Lcom/example/splitexpenses/data/Category;", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGroupMembers", "updateGroupName", "newName", "updateMemberAvatar", "avatarEmoji", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateMemberName", "oldMemberName", "newMemberName", "app_debug"})
public final class GroupRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.OfflineCapableRepository offlineCapableRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.source.LocalDataSource localDataSource = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "GroupRepository";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.splitexpenses.data.GroupData> _currentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> currentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.splitexpenses.data.GroupData>> _availableGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.splitexpenses.data.GroupData>> availableGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _currentGroupError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> currentGroupError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _groupsError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> groupsError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoadingCurrentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingCurrentGroup = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoadingGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingGroups = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _accessLost = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> accessLost = null;
    
    @javax.inject.Inject()
    public GroupRepository(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.OfflineCapableRepository offlineCapableRepository, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.source.LocalDataSource localDataSource) {
        super();
    }
    
    /**
     * Get the application context
     * @return The application context
     */
    @org.jetbrains.annotations.NotNull()
    public final android.content.Context getContext() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.data.GroupData> getCurrentGroup() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.splitexpenses.data.GroupData>> getAvailableGroups() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getCurrentGroupError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getGroupsError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingCurrentGroup() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoadingGroups() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getAccessLost() {
        return null;
    }
    
    /**
     * Get the most recent expense date for a group
     * @param group The group to check
     * @return The timestamp of the most recent expense, or 0 if there are no expenses
     */
    private final long getMostRecentExpenseDate(com.example.splitexpenses.data.GroupData group) {
        return 0L;
    }
    
    /**
     * Start listening for real-time updates to available groups
     */
    public final void startListeningForAvailableGroups() {
    }
    
    /**
     * Start listening for real-time updates to the current group
     * @param groupId The ID of the group to listen to
     */
    public final void startListeningForCurrentGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
    }
    
    /**
     * Stop listening for real-time updates to the current group
     */
    public final void stopListeningForCurrentGroup() {
    }
    
    /**
     * Reset the access lost flag (call this when navigating back to group list)
     */
    public final void resetAccessLost() {
    }
    
    /**
     * Create a new group
     * @param name The name of the group
     * @param members The initial members of the group
     * @param currentUser The current user's name
     * @return The ID of the newly created group
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> members, @org.jetbrains.annotations.NotNull()
    java.lang.String currentUser, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Join an existing group
     * @param groupId The ID of the group to join
     * @param memberName The name of the member joining the group
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object joinGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Delete a group
     * @param groupId The ID of the group to delete
     * @return True if the group was successfully deleted, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update the members of a group
     * @param groupId The ID of the group to update
     * @param members The new list of members
     * @return True if the members were successfully updated, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupMembers(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> members, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Export a group to CSV
     * @param outputStream The output stream to write to
     * @return True if export was successful, false otherwise
     */
    public final boolean exportGroupToCsv(@org.jetbrains.annotations.NotNull()
    java.io.OutputStream outputStream) {
        return false;
    }
    
    /**
     * Import a group from CSV with enhanced user management support
     * @param inputStream The input stream to read from
     * @param currentUser The current user's name (optional)
     * @return The result of the import operation
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object importGroupFromCsv(@org.jetbrains.annotations.NotNull()
    java.io.InputStream inputStream, @org.jetbrains.annotations.Nullable()
    java.lang.String currentUser, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.splitexpenses.util.CsvImportResult> $completion) {
        return null;
    }
    
    /**
     * Get the saved user name for a group
     * @param groupId The ID of the group
     * @return The user name or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSavedUserForGroup(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return null;
    }
    
    /**
     * Gets a list of unassigned member names (members without an associated UID)
     * @param groupId The ID of the group
     * @return List of unassigned member names
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnassignedMembers(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * Gets information about all members in a group, with their assignment status
     * @param groupId The ID of the group
     * @return Pair of (all members, assigned members)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getGroupMembersWithStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Pair<? extends java.util.List<java.lang.String>, ? extends java.util.List<java.lang.String>>> $completion) {
        return null;
    }
    
    /**
     * Check if the current user is the creator of a group
     * This method works for any group, not just the current one
     * @param groupId The ID of the group
     * @return True if the current user is the creator, false otherwise
     */
    public final boolean isCurrentUserGroupCreator(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId) {
        return false;
    }
    
    /**
     * Removes a member from a group and kicks them out (removes from allowedUsers)
     * This should only be called by the group creator
     * @param groupId The ID of the group
     * @param memberName The name of the member to remove
     * @return True if successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeMemberAndKick(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Load available groups once (for one-time operations)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object loadAvailableGroups(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Update the categories for a group
     * @param groupId The ID of the group
     * @param categories The updated list of categories
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupCategories(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Category> categories, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update the group name
     * @param groupId The ID of the group to update
     * @param newName The new name for the group
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGroupName(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String newName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update a member's avatar
     * @param groupId The ID of the group
     * @param memberName The name of the member
     * @param avatarEmoji The emoji to use as the avatar, or null to remove the avatar
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateMemberAvatar(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName, @org.jetbrains.annotations.Nullable()
    java.lang.String avatarEmoji, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Update a member's name while preserving their UID
     * @param groupId The ID of the group
     * @param oldMemberName The current name of the member
     * @param newMemberName The new name for the member
     * @return True if the update was successful, false otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateMemberName(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String oldMemberName, @org.jetbrains.annotations.NotNull()
    java.lang.String newMemberName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Get the avatar for a member
     * @param groupId The ID of the group
     * @param memberName The name of the member
     * @return The avatar emoji or null if not found
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMemberAvatar(@org.jetbrains.annotations.NotNull()
    java.lang.String groupId, @org.jetbrains.annotations.NotNull()
    java.lang.String memberName) {
        return null;
    }
    
    /**
     * Get pending sync count for offline changes
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Integer> getPendingSyncCountFlow() {
        return null;
    }
    
    /**
     * Force sync offline changes
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object forceSyncOfflineChanges(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}